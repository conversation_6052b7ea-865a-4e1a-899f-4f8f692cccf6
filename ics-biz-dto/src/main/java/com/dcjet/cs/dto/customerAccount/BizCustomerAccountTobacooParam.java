package com.dcjet.cs.dto.customerAccount;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import com.xdo.validation.annotation.XdoSize;
/**
 *
 * <AUTHOR>
 * @date: 2025-7-2
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class BizCustomerAccountTobacooParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 业务类型
     */
	@XdoSize(max = 120, message = "业务类型长度不能超过120位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("业务类型")
	private  String businessType;
	/**
     * 结算单号
     */
	@XdoSize(max = 120, message = "结算单号长度不能超过120位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("结算单号")
	private  String accountNo;
	/**
     * 合同号
     */
	@XdoSize(max = 120, message = "合同号长度不能超过120位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("合同号")
	private  String contractNo;
	/**
     * 币种
     */
	@XdoSize(max = 20, message = "币种长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("币种")
	private  String curr;
	/**
     * 汇率
     */
	@Digits(integer = 13, fraction = 6, message = "汇率必须为数字,整数位最大13位,小数最大6位!")
	@ApiModelProperty("汇率")
	private  BigDecimal exchangeRate;
	/**
     * 货款
     */
	@Digits(integer = 17, fraction = 2, message = "货款必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("货款")
	private  BigDecimal goodsPrice;
	/**
     * 代理费率%
     */
	@Digits(integer = 15, fraction = 4, message = "代理费率%必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("代理费率%")
	private  BigDecimal agentFeeRate;
	/**
     * 代理费（不含税）
     */
	@Digits(integer = 17, fraction = 2, message = "代理费（不含税）必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("代理费（不含税）")
	private  BigDecimal agentFee;
	/**
     * 代理费税额
     */
	@Digits(integer = 17, fraction = 2, message = "代理费税额必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("代理费税额")
	private  BigDecimal agentTaxFee;
	/**
     * 代理费（价税合计）
     */
	@Digits(integer = 17, fraction = 2, message = "代理费（价税合计）必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("代理费（价税合计）")
	private  BigDecimal agentFeeTotal;
	/**
     * 结算日期
     */
	@ApiModelProperty("结算日期")
	private  Date businessDate;
	/**
     * 发送财务系统
     */
	@XdoSize(max = 20, message = "发送财务系统长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("发送财务系统")
	private  String sendFinance;
	/**
     * 商品与数量
     */
	@XdoSize(max = 1000, message = "商品与数量长度不能超过1000位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("商品与数量")
	private  String producrSome;
	/**
     * 备注
     */
	@XdoSize(max = 1000, message = "备注长度不能超过1000位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("备注")
	private  String note;
	/**
     * 单据状态
     */
	@XdoSize(max = 20, message = "单据状态长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("单据状态")
	private  String status;
	/**
     * 审核状态
     */
	@XdoSize(max = 20, message = "审核状态长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("审核状态")
	private  String apprStatus;
	/**
     * 确认时间
     */
	@ApiModelProperty("确认时间")
	private  Date confirmTime;
	/**
     * 人民币货款
     */
	@Digits(integer = 17, fraction = 2, message = "人民币货款必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("人民币货款")
	private  BigDecimal goodsPriceRmb;
	/**
     * 增值税率%
     */
	@Digits(integer = 15, fraction = 4, message = "增值税率%必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("增值税率%")
	private  BigDecimal vatRate;
	/**
     * 客户
     */
	@XdoSize(max = 400, message = "客户长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("客户")
	private  String customer;


	private String createTimeFrom;

	private String createTimeTo;

	private String createrBy;

	private String createrUserName;

	private Date createrTime;
}
