package com.dcjet.cs.dto.equipment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import com.xdo.validation.annotation.XdoSize;
/**
 *
 * <AUTHOR>
 * @date: 2025-7-2
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class BizIEquipmentAgentAgreementParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 主键id
     */
	@NotEmpty(message="主键id不能为空！")
	@XdoSize(max = 40, message = "主键id长度不能超过40位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("主键id")
	private  String id;
	/**
     * 业务类型
     */
	@XdoSize(max = 60, message = "业务类型长度不能超过60位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("业务类型")
	private  String businessType;
	/**
     * 业务单号
     */
	@XdoSize(max = 10, message = "业务单号长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("业务单号")
	private  String tradeCode;
	/**
     * 组织机构代码
     */
	@XdoSize(max = 10, message = "组织机构代码长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("组织机构代码")
	private  String sysOrgCode;
	/**
     * 数据状态
     */
	@XdoSize(max = 10, message = "数据状态长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("数据状态")
	private  String dataState;
	/**
     * 版本号
     */
	@XdoSize(max = 10, message = "版本号长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("版本号")
	private  String versionNo;
	/**
     * 父id
     */
	@XdoSize(max = 40, message = "父id长度不能超过40位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("父id")
	private  String parentId;
	/**
     * 创建人账号
     */
	@NotEmpty(message="创建人账号不能为空！")
	@XdoSize(max = 50, message = "创建人账号长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("创建人账号")
	private  String createBy;
	/**
     * 创建时间
     */
	@NotNull(message="创建时间不能为空！")
	@ApiModelProperty("创建时间")
	private  Date createTime;
	/**
     * 修改人账号
     */
	@XdoSize(max = 50, message = "修改人账号长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("修改人账号")
	private  String updateBy;
	/**
     * 创建人姓名
     */
	@XdoSize(max = 50, message = "创建人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("创建人姓名")
	private  String insertUserName;
	/**
     * 修改人姓名
     */
	@XdoSize(max = 50, message = "修改人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("修改人姓名")
	private  String updateUserName;
	/**
     * 扩展字段1
     */
	@XdoSize(max = 200, message = "扩展字段1长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段1")
	private  String extend1;
	/**
     * 扩展字段2
     */
	@XdoSize(max = 200, message = "扩展字段2长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段2")
	private  String extend2;
	/**
     * 扩展字段3
     */
	@XdoSize(max = 200, message = "扩展字段3长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段3")
	private  String extend3;
	/**
     * 扩展字段4
     */
	@XdoSize(max = 200, message = "扩展字段4长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段4")
	private  String extend4;
	/**
     * 扩展字段5
     */
	@XdoSize(max = 200, message = "扩展字段5长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段5")
	private  String extend5;
	/**
     * 扩展字段6
     */
	@XdoSize(max = 200, message = "扩展字段6长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段6")
	private  String extend6;
	/**
     * 扩展字段7
     */
	@XdoSize(max = 200, message = "扩展字段7长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段7")
	private  String extend7;
	/**
     * 扩展字段8
     */
	@XdoSize(max = 200, message = "扩展字段8长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段8")
	private  String extend8;
	/**
     * 扩展字段9
     */
	@XdoSize(max = 200, message = "扩展字段9长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段9")
	private  String extend9;
	/**
     * 扩展字段10
     */
	@XdoSize(max = 200, message = "扩展字段10长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段10")
	private  String extend10;
	/**
     * 合同号
     */
	@XdoSize(max = 60, message = "合同号长度不能超过60位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("合同号")
	private  String contractNo;
	/**
     * 业务地点
     */
	@XdoSize(max = 20, message = "业务地点长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("业务地点")
	private  String businessPlace;
	/**
     * 协议类型
     */
	@XdoSize(max = 20, message = "协议类型长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("协议类型")
	private  String agreementType;
	/**
     * 协议编号
     */
	@XdoSize(max = 60, message = "协议编号长度不能超过60位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("协议编号")
	private  String agreementNo;
	/**
     * 客户
     */
	@XdoSize(max = 200, message = "客户长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("客户")
	private  String customer;
	/**
     * 供应商
     */
	@XdoSize(max = 200, message = "供应商长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("供应商")
	private  String supplier;
	/**
     * 签约日期
     */
	@ApiModelProperty("签约日期")
	private  Date signDate;
	/**
    * 签约日期-开始
    */
	@ApiModelProperty("签约日期-开始")
	private String signDateFrom;
	/**
    * 签约日期-结束
    */
	@ApiModelProperty("签约日期-结束")
    private String signDateTo;
	/**
     * 签约地点
     */
	@XdoSize(max = 200, message = "签约地点长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("签约地点")
	private  String signPlace;
	/**
     * 币种
     */
	@XdoSize(max = 10, message = "币种长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("币种")
	private  String currency;
	/**
     * 合同金额
     */
	@Digits(integer = 15, fraction = 4, message = "合同金额必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("合同金额")
	private  BigDecimal contractAmount;
	/**
     * 代理费率%
     */
	@Digits(integer = 17, fraction = 2, message = "代理费率%必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("代理费率%")
	private  BigDecimal agencyRate;
	/**
     * 代理费用
     */
	@Digits(integer = 17, fraction = 2, message = "代理费用必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("代理费用")
	private  BigDecimal agencyFee;
	/**
     * 协议条款
     */
	@XdoSize(max = 1000, message = "协议条款长度不能超过1000位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("协议条款")
	private  String agreementTerms;
	/**
     * 制单人
     */
	@XdoSize(max = 10, message = "制单人长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("制单人")
	private  String makeBy;
	/**
     * 制单日期
     */
	@ApiModelProperty("制单日期")
	private  Date makeDate;
	/**
    * 制单日期-开始
    */
	@ApiModelProperty("制单日期-开始")
	private String makeDateFrom;
	/**
    * 制单日期-结束
    */
	@ApiModelProperty("制单日期-结束")
    private String makeDateTo;
	/**
     * 单据状态
     */
	@XdoSize(max = 10, message = "单据状态长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("单据状态")
	private  String billStatus;
	/**
     * 确认时间
     */
	@ApiModelProperty("确认时间")
	private  Date confirmTime;
	/**
     * 审批状态
     */
	@XdoSize(max = 10, message = "审批状态长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("审批状态")
	private  String approvalStatus;
}
