package com.dcjet.cs.dto.customerAccount;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import com.xdo.validation.annotation.XdoSize;
/**
 *
 * <AUTHOR>
 * @date: 2025-7-1
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class BizCustomerAccountSummaryParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 创建人
     */
	@NotEmpty(message="创建人不能为空！")
	@XdoSize(max = 50, message = "创建人长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("创建人")
	private  String createBy;
	/**
     * 创建时间
     */
	@NotNull(message="创建时间不能为空！")
	@ApiModelProperty("创建时间")
	private  Date createTime;
	/**
     * 创建人名称
     */
	@XdoSize(max = 50, message = "创建人名称长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("创建人名称")
	private  String createUserName;
	/**
     * 创建人
     */
	@XdoSize(max = 50, message = "创建人长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("创建人")
	private  String updateBy;
	/**
     * 最后修改人名称
     */
	@XdoSize(max = 50, message = "最后修改人名称长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("最后修改人名称")
	private  String updateUserName;
	/**
     * 企业编码
     */
	@XdoSize(max = 50, message = "企业编码长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("企业编码")
	private  String tradeCode;
	/**
     * 创建人部门编码
     */
	@XdoSize(max = 50, message = "创建人部门编码长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("创建人部门编码")
	private  String sysOrgCode;
	/**
     * 拓展字段1
     */
	@XdoSize(max = 200, message = "拓展字段1长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拓展字段1")
	private  String extend1;
	/**
     * 拓展字段2
     */
	@XdoSize(max = 200, message = "拓展字段2长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拓展字段2")
	private  String extend2;
	/**
     * 拓展字段3
     */
	@XdoSize(max = 200, message = "拓展字段3长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拓展字段3")
	private  String extend3;
	/**
     * 拓展字段4
     */
	@XdoSize(max = 200, message = "拓展字段4长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拓展字段4")
	private  String extend4;
	/**
     * 拓展字段5
     */
	@XdoSize(max = 200, message = "拓展字段5长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拓展字段5")
	private  String extend5;
	/**
     * 拓展字段6
     */
	@XdoSize(max = 200, message = "拓展字段6长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拓展字段6")
	private  String extend6;
	/**
     * 拓展字段7
     */
	@XdoSize(max = 200, message = "拓展字段7长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拓展字段7")
	private  String extend7;
	/**
     * 拓展字段8
     */
	@XdoSize(max = 200, message = "拓展字段8长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拓展字段8")
	private  String extend8;
	/**
     * 拓展字段9
     */
	@XdoSize(max = 200, message = "拓展字段9长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拓展字段9")
	private  String extend9;
	/**
     * 拓展字段10
     */
	@XdoSize(max = 200, message = "拓展字段10长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("拓展字段10")
	private  String extend10;
	/**
     * 业务类型
     */
	@XdoSize(max = 120, message = "业务类型长度不能超过120位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("业务类型")
	private  String businessType;
	/**
     * 结算单号
     */
	@XdoSize(max = 120, message = "结算单号长度不能超过120位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("结算单号")
	private  String accountNo;
	/**
     * 合同号
     */
	@XdoSize(max = 120, message = "合同号长度不能超过120位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("合同号")
	private  String contractNo;
	/**
     * 币种
     */
	@XdoSize(max = 20, message = "币种长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("币种")
	private  String curr;
	/**
     * 汇率
     */
	@Digits(integer = 13, fraction = 6, message = "汇率必须为数字,整数位最大13位,小数最大6位!")
	@ApiModelProperty("汇率")
	private  BigDecimal exchangeRate;
	/**
     * 货款
     */
	@Digits(integer = 17, fraction = 2, message = "货款必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("货款")
	private  BigDecimal goodsPrice;
	/**
     * 代理费率%
     */
	@Digits(integer = 15, fraction = 4, message = "代理费率%必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("代理费率%")
	private  BigDecimal agentFeeRate;
	/**
     * 代理费（不含税）
     */
	@Digits(integer = 17, fraction = 2, message = "代理费（不含税）必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("代理费（不含税）")
	private  BigDecimal agentFee;
	/**
     * 代理费税额
     */
	@Digits(integer = 17, fraction = 2, message = "代理费税额必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("代理费税额")
	private  BigDecimal agentTaxFee;
	/**
     * 代理费（价税合计）
     */
	@Digits(integer = 17, fraction = 2, message = "代理费（价税合计）必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("代理费（价税合计）")
	private  BigDecimal agentFeeTotal;
	/**
     * 结算日期
     */
	@ApiModelProperty("结算日期")
	private  Date businessDate;
	/**
     * 商品名称
     */
	@XdoSize(max = 160, message = "商品名称长度不能超过160位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("商品名称")
	@JsonProperty("gName")
	private  String gName;
	/**
     * 发送财务系统
     */
	@XdoSize(max = 20, message = "发送财务系统长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("发送财务系统")
	private  String sendFinance;
	/**
     * 商品与数量
     */
	@XdoSize(max = 1000, message = "商品与数量长度不能超过1000位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("商品与数量")
	private  String producrSome;
	/**
     * 备注
     */
	@XdoSize(max = 1000, message = "备注长度不能超过1000位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("备注")
	private  String note;
	/**
     * 是否红冲
     */
	@XdoSize(max = 20, message = "是否红冲长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("是否红冲")
	private  String redFlush;
	/**
     * 单据状态
     */
	@XdoSize(max = 20, message = "单据状态长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("单据状态")
	private  String status;
	/**
     * 审核状态
     */
	@XdoSize(max = 20, message = "审核状态长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("审核状态")
	private  String apprStatus;
	/**
     * 确认时间
     */
	@ApiModelProperty("确认时间")
	private  Date confirmTime;
	/**
     * 是否确认
     */
	@XdoSize(max = 10, message = "是否确认长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("是否确认")
	private  String isConfirm;
	/**
     * 外商合同、进货明细数据标记
     */
	@XdoSize(max = 20, message = "外商合同、进货明细数据标记长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("外商合同、进货明细数据标记")
	private  String purchaseMark;
	/**
     * 外商合同、进货明细数据标记
     */
	@XdoSize(max = 600, message = "外商合同、进货明细数据标记长度不能超过600位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("外商合同、进货明细数据标记")
	private  String purchaseNoMark;
	/**
     * 人民币货款
     */
	@Digits(integer = 17, fraction = 2, message = "人民币货款必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("人民币货款")
	private  BigDecimal goodsPriceRmb;
	/**
     * 增值税率%
     */
	@Digits(integer = 15, fraction = 4, message = "增值税率%必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("增值税率%")
	private  BigDecimal vatRate;
	/**
     * 货代费
     */
	@Digits(integer = 17, fraction = 2, message = "货代费必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("货代费")
	private  BigDecimal freightForwardingFee;
	/**
     * 保险费
     */
	@Digits(integer = 17, fraction = 2, message = "保险费必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("保险费")
	private  BigDecimal insuranceFee;
	/**
     * 已结算款项合计
     */
	@Digits(integer = 17, fraction = 2, message = "已结算款项合计必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("已结算款项合计")
	private  BigDecimal costFee;
	/**
     * 实际预收款
     */
	@Digits(integer = 17, fraction = 2, message = "实际预收款必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("实际预收款")
	private  BigDecimal depositReceived;
	/**
     * 应退款项
     */
	@Digits(integer = 17, fraction = 2, message = "应退款项必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("应退款项")
	private  BigDecimal refundFee;
	private  String customer;

	private String createTimeFrom;

	private String createTimeTo;

	private String createrBy;

	private String createrUserName;

	private Date createrTime;
}
