package com.dcjet.cs.api.customerAccount;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.customerAccount.BizCustomerAccountParam;
import com.dcjet.cs.dto.customerAccount.BizCustomerAccountSummaryDto;
import com.dcjet.cs.dto.customerAccount.BizCustomerAccountSummaryParam;
import com.dcjet.cs.dto.customerAccount.BizCustomerAccountSummaryExportParam;
import com.dcjet.cs.customerAccount.service.BizCustomerAccountSummaryService;
import com.dcjet.cs.dto.warehouse.BizStoreIHeadParam;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.common.token.UserInfoToken;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-7-1
 */
@RestController
@RequestMapping("v1/bizCustomerAccountSummary")
@Api(tags = "接口")
public class BizCustomerAccountSummaryController extends BaseController {
    @Resource
    private BizCustomerAccountSummaryService bizCustomerAccountSummaryService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    /**
     * @param bizCustomerAccountSummaryParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BizCustomerAccountSummaryDto>> getListPaged(@RequestBody BizCustomerAccountSummaryParam bizCustomerAccountSummaryParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizCustomerAccountSummaryDto>> paged = bizCustomerAccountSummaryService.getListPaged(bizCustomerAccountSummaryParam, pageParam,userInfo);
        return paged;
    }
    /**
     * @param bizCustomerAccountSummaryParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizCustomerAccountSummaryDto> insert(@Valid @RequestBody BizCustomerAccountSummaryParam bizCustomerAccountSummaryParam, UserInfoToken userInfo) {
        ResultObject<BizCustomerAccountSummaryDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizCustomerAccountSummaryDto bizCustomerAccountSummaryDto = bizCustomerAccountSummaryService.insert(bizCustomerAccountSummaryParam, userInfo);
        if (bizCustomerAccountSummaryDto != null) {
            resultObject.setData(bizCustomerAccountSummaryDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param bizCustomerAccountSummaryParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizCustomerAccountSummaryDto> update(@PathVariable String sid, @Valid @RequestBody BizCustomerAccountSummaryParam bizCustomerAccountSummaryParam, UserInfoToken userInfo) {
        bizCustomerAccountSummaryParam.setSid(sid);
        BizCustomerAccountSummaryDto bizCustomerAccountSummaryDto = bizCustomerAccountSummaryService.update(bizCustomerAccountSummaryParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizCustomerAccountSummaryDto != null) {
            resultObject.setData(bizCustomerAccountSummaryDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 bizCustomerAccountSummaryService.delete(sids, userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizCustomerAccountSummaryExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizCustomerAccountSummaryDto> bizCustomerAccountSummaryDtos = bizCustomerAccountSummaryService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizCustomerAccountSummaryDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizCustomerAccountSummaryDtos);
    }
    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizCustomerAccountSummaryDto> list) {
        for(BizCustomerAccountSummaryDto item : list) {
        }
    }
    @ApiOperation("发送审批接口")
    @PostMapping("sendApproval/{sid}")
    public ResultObject sendApproval(@PathVariable String sid, UserInfoToken userInfo) {
        return bizCustomerAccountSummaryService.sendApproval(sid, userInfo);
    }
    @ApiOperation("确认数据状态接口")
    @PostMapping("confirm")
    public ResultObject confirmStatus(@RequestBody BizCustomerAccountSummaryParam param, UserInfoToken userInfo) {
        return bizCustomerAccountSummaryService.confirmStatus(param, userInfo);
    }
    @ApiOperation("作废接口")
    @PostMapping("invalidate/{sid}")
    public ResultObject invalidate(@PathVariable String sid, UserInfoToken userInfo) {
        return bizCustomerAccountSummaryService.invalidate(sid, userInfo);
    }
    @ApiOperation("打印联系单")
    @PostMapping("/printContact")
    public ResponseEntity printContact(@RequestBody BizCustomerAccountSummaryParam param, UserInfoToken userInfo) throws Exception {
        return bizCustomerAccountSummaryService.printContact(param, userInfo);
    }
    @ApiOperation("打印结算单")
    @PostMapping("/printSummary")
    public ResponseEntity printSummary(@RequestBody BizCustomerAccountSummaryParam param, UserInfoToken userInfo) throws Exception {
        return bizCustomerAccountSummaryService.printSummary(param, userInfo);
    }
    @ApiOperation("获取流水号")
    @PostMapping("getSerialNo")
    public ResultObject getSerialNo(UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取流水号成功");
        String serialNo = bizCustomerAccountSummaryService.getSerialNo(userInfo);
        resultObject.setData(serialNo);
        return resultObject;
    }
    @ApiOperation("获取供应商列表信息")
    @PostMapping("/getSupplierList")
    public ResultObject getSupplierList(BizCustomerAccountSummaryParam params, UserInfoToken userInfo) {
        return bizCustomerAccountSummaryService.getSupplierList(params,userInfo);
    }
    @ApiOperation("获取制单人列表信息")
    @PostMapping("/getCreateUserList")
    public ResultObject getCreateUserList(BizCustomerAccountSummaryParam params, UserInfoToken userInfo) {
        return bizCustomerAccountSummaryService.getCreateUserList(params,userInfo);
    }
}
