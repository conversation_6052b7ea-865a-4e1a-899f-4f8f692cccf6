package com.dcjet.cs.api.equipment;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.equipment.BizIEquipmentAgentAgreementDto;
import com.dcjet.cs.dto.equipment.BizIEquipmentAgentAgreementParam;
import com.dcjet.cs.dto.equipment.BizIEquipmentAgentAgreementExportParam;
import com.dcjet.cs.equipment.service.BizIEquipmentAgentAgreementService;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.common.token.UserInfoToken;
import com.xdo.springboot.BaseController;
import com.xdo.springboot.annotation.FuYunMenuAuthentication;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-7-2
 */
@RestController
@RequestMapping("v1/bizIEquipmentAgentAgreement")
@Api(tags = "接口")
public class BizIEquipmentAgentAgreementController extends BaseController {
    @Resource
    private BizIEquipmentAgentAgreementService bizIEquipmentAgentAgreementService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    /**
     * @param bizIEquipmentAgentAgreementParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    @FuYunMenuAuthentication("/tobacco/iEquipment/proxyAgreement")
    public ResultObject<List<BizIEquipmentAgentAgreementDto>> getListPaged(@RequestBody BizIEquipmentAgentAgreementParam bizIEquipmentAgentAgreementParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizIEquipmentAgentAgreementDto>> paged = bizIEquipmentAgentAgreementService.getListPaged(bizIEquipmentAgentAgreementParam, pageParam);
        return paged;
    }
    /**
     * @param bizIEquipmentAgentAgreementParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizIEquipmentAgentAgreementDto> insert(@Valid @RequestBody BizIEquipmentAgentAgreementParam bizIEquipmentAgentAgreementParam, UserInfoToken userInfo) {
        ResultObject<BizIEquipmentAgentAgreementDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizIEquipmentAgentAgreementDto bizIEquipmentAgentAgreementDto = bizIEquipmentAgentAgreementService.insert(bizIEquipmentAgentAgreementParam, userInfo);
        if (bizIEquipmentAgentAgreementDto != null) {
            resultObject.setData(bizIEquipmentAgentAgreementDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param bizIEquipmentAgentAgreementParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizIEquipmentAgentAgreementDto> update(@PathVariable String sid, @Valid @RequestBody BizIEquipmentAgentAgreementParam bizIEquipmentAgentAgreementParam, UserInfoToken userInfo) {
        bizIEquipmentAgentAgreementParam.setSid(sid);
        BizIEquipmentAgentAgreementDto bizIEquipmentAgentAgreementDto = bizIEquipmentAgentAgreementService.update(bizIEquipmentAgentAgreementParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizIEquipmentAgentAgreementDto != null) {
            resultObject.setData(bizIEquipmentAgentAgreementDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 bizIEquipmentAgentAgreementService.delete(sids, userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizIEquipmentAgentAgreementExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizIEquipmentAgentAgreementDto> bizIEquipmentAgentAgreementDtos = bizIEquipmentAgentAgreementService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizIEquipmentAgentAgreementDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizIEquipmentAgentAgreementDtos);
    }
    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizIEquipmentAgentAgreementDto> list) {
        for(BizIEquipmentAgentAgreementDto item : list) {
        }
    }
}
