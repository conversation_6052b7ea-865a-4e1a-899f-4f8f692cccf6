package com.dcjet.cs.api.equipment;
import com.dcjet.cs.dto.equipment.BizIEquipmentAgentAgreementListDto;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.equipment.BizIEquipmentAgentAgreementListDto;
import com.dcjet.cs.dto.equipment.BizIEquipmentAgentAgreementListParam;
import com.dcjet.cs.dto.equipment.BizIEquipmentAgentAgreementListExportParam;
import com.dcjet.cs.equipment.service.BizIEquipmentAgentAgreementListService;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-7-2
 */
@RestController
@RequestMapping("v1/bizIEquipmentAgentAgreementList")
@Api(tags = "接口")
public class BizIEquipmentAgentAgreementListController extends BaseController {
    @Resource
    private BizIEquipmentAgentAgreementListService bizIEquipmentAgentAgreementListService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    /**
     * @param bizIEquipmentAgentAgreementListParam
     * @param pageParam
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BizIEquipmentAgentAgreementListDto>> getListPaged(@RequestBody BizIEquipmentAgentAgreementListParam bizIEquipmentAgentAgreementListParam, PageParam pageParam) {
        ResultObject<List<BizIEquipmentAgentAgreementListDto>> paged = bizIEquipmentAgentAgreementListService.selectAllPaged(bizIEquipmentAgentAgreementListParam, pageParam);
        return paged;
    }
    /**
     * @param bizIEquipmentAgentAgreementListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizIEquipmentAgentAgreementListDto> insert(@Valid @RequestBody BizIEquipmentAgentAgreementListParam bizIEquipmentAgentAgreementListParam, UserInfoToken userInfo) {
		ResultObject<BizIEquipmentAgentAgreementListDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizIEquipmentAgentAgreementListDto bizIEquipmentAgentAgreementListDto = bizIEquipmentAgentAgreementListService.insert(bizIEquipmentAgentAgreementListParam, userInfo);
        if (bizIEquipmentAgentAgreementListDto != null) {
            resultObject.setData(bizIEquipmentAgentAgreementListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param bizIEquipmentAgentAgreementListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizIEquipmentAgentAgreementListDto> update(@PathVariable String sid, @Valid @RequestBody BizIEquipmentAgentAgreementListParam bizIEquipmentAgentAgreementListParam, UserInfoToken userInfo) {
        bizIEquipmentAgentAgreementListParam.setSid(sid);
        BizIEquipmentAgentAgreementListDto bizIEquipmentAgentAgreementListDto = bizIEquipmentAgentAgreementListService.update(bizIEquipmentAgentAgreementListParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizIEquipmentAgentAgreementListDto != null) {
            resultObject.setData(bizIEquipmentAgentAgreementListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
		ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        bizIEquipmentAgentAgreementListService.delete(sids, userInfo);
        return resultObject;
    }
}
