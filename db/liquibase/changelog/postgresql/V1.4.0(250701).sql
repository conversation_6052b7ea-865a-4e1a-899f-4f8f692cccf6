--liquibase formatted sql

--changeset xbxu1:
-- 1）贸易国别（长度 60 × 2 = 120）
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_MERCHANT" ADD if not EXISTS "TRADE_COUNTRY" VARCHAR(120);
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_MERCHANT"."TRADE_COUNTRY" IS '贸易国别';

-- 2）装运人 SHIPPER（长度 300 × 2 = 600）
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_MERCHANT" ADD if not EXISTS "SHIPPER" VARCHAR(600);
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_MERCHANT"."SHIPPER" IS '装运人';

-- 3）收货人 CONSIGNEE（长度 300 × 2 = 600）
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_MERCHANT" ADD if not EXISTS "CONSIGNEE" VARCHAR(600);
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_MERCHANT"."CONSIGNEE" IS '收货人';

-- 4）通知人 NOTIFY PARTY（长度 300 × 2 = 600）
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_MERCHANT" ADD if not EXISTS "NOTIFY_PARTY" VARCHAR(600);
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_MERCHANT"."NOTIFY_PARTY" IS '通知人';

-- 5）仓储地址（长度 300 × 2 = 600）
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_MERCHANT" ADD if not EXISTS "WAREHOUSE_ADDRESS" VARCHAR(600);
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_MERCHANT"."WAREHOUSE_ADDRESS" IS '仓储地址';

-- 6）联系人（长度 20 × 2 = 40）
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_MERCHANT" ADD if not EXISTS "CONTACT_PERSON" VARCHAR(40);
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_MERCHANT"."CONTACT_PERSON" IS '联系人';

-- 7）联系电话（长度 20 × 2 = 40）
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_MERCHANT" ADD if not EXISTS "CONTACT_PHONE" VARCHAR(40);
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_MERCHANT"."CONTACT_PHONE" IS '联系电话';