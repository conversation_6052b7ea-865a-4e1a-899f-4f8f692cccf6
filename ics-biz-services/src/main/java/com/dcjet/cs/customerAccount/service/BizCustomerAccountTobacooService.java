package com.dcjet.cs.customerAccount.service;
import com.dcjet.cs.customerAccount.model.BizCustomerAccountSummary;
import com.dcjet.cs.util.CommonEnum;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.customerAccount.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.customerAccount.dao.BizCustomerAccountTobacooMapper;
import com.dcjet.cs.customerAccount.mapper.BizCustomerAccountTobacooDtoMapper;
import com.dcjet.cs.customerAccount.model.BizCustomerAccountTobacoo;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import jdk.nashorn.internal.runtime.options.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-7-2
 */
@Service
public class BizCustomerAccountTobacooService extends BaseService<BizCustomerAccountTobacoo> {
    @Resource
    private BizCustomerAccountTobacooMapper bizCustomerAccountTobacooMapper;
    @Resource
    private BizCustomerAccountTobacooDtoMapper bizCustomerAccountTobacooDtoMapper;
    @Override
    public Mapper<BizCustomerAccountTobacoo> getMapper() {
        return bizCustomerAccountTobacooMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizCustomerAccountTobacooParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizCustomerAccountTobacooDto>> getListPaged(BizCustomerAccountTobacooParam bizCustomerAccountTobacooParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizCustomerAccountTobacoo bizCustomerAccountTobacoo = bizCustomerAccountTobacooDtoMapper.toPo(bizCustomerAccountTobacooParam);
        bizCustomerAccountTobacoo.setTradeCode(userInfo.getCompany());
        Page<BizCustomerAccountTobacoo> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizCustomerAccountTobacooMapper.getList(bizCustomerAccountTobacoo));
        List<BizCustomerAccountTobacooDto> bizCustomerAccountTobacooDtos = page.getResult().stream().map(head -> {
            BizCustomerAccountTobacooDto dto = bizCustomerAccountTobacooDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizCustomerAccountTobacooDto>> paged = ResultObject.createInstance(bizCustomerAccountTobacooDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizCustomerAccountTobacooParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizCustomerAccountTobacooDto insert(BizCustomerAccountTobacooParam bizCustomerAccountTobacooParam, UserInfoToken userInfo) {
        BizCustomerAccountTobacoo bizCustomerAccountTobacoo = bizCustomerAccountTobacooDtoMapper.toPo(bizCustomerAccountTobacooParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizCustomerAccountTobacoo.setSid(sid);
        bizCustomerAccountTobacoo.setCreateBy(userInfo.getUserNo());
        bizCustomerAccountTobacoo.setCreateTime(new Date());
        bizCustomerAccountTobacoo.setCreateUserName(userInfo.getUserName());
        bizCustomerAccountTobacoo.setBusinessType("3");
        bizCustomerAccountTobacoo.setApprStatus("0");
        bizCustomerAccountTobacoo.setStatus("0");
        bizCustomerAccountTobacoo.setSendFinance("0");
        bizCustomerAccountTobacoo.setVatRate(new BigDecimal(6));
        bizCustomerAccountTobacoo.setBusinessDate(new Date());
        // 新增数据
        int insertStatus = bizCustomerAccountTobacooMapper.insert(bizCustomerAccountTobacoo);
        return  insertStatus > 0 ? bizCustomerAccountTobacooDtoMapper.toDto(bizCustomerAccountTobacoo) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizCustomerAccountTobacooParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizCustomerAccountTobacooDto update(BizCustomerAccountTobacooParam bizCustomerAccountTobacooParam, UserInfoToken userInfo) {
        BizCustomerAccountTobacoo bizCustomerAccountTobacoo = bizCustomerAccountTobacooMapper.selectByPrimaryKey(bizCustomerAccountTobacooParam.getSid());
        bizCustomerAccountTobacooDtoMapper.updatePo(bizCustomerAccountTobacooParam, bizCustomerAccountTobacoo);
        bizCustomerAccountTobacoo.setUpdateBy(userInfo.getUserNo());
        bizCustomerAccountTobacoo.setUpdateTime(new Date());
        bizCustomerAccountTobacoo.setUpdateUserName(userInfo.getUserName());
        // 更新数据
        int update = bizCustomerAccountTobacooMapper.updateByPrimaryKey(bizCustomerAccountTobacoo);
        return update > 0 ? bizCustomerAccountTobacooDtoMapper.toDto(bizCustomerAccountTobacoo) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		bizCustomerAccountTobacooMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizCustomerAccountTobacooDto> selectAll(BizCustomerAccountTobacooParam exportParam, UserInfoToken userInfo) {
        BizCustomerAccountTobacoo bizCustomerAccountTobacoo = bizCustomerAccountTobacooDtoMapper.toPo(exportParam);
         bizCustomerAccountTobacoo.setTradeCode(userInfo.getCompany());
        List<BizCustomerAccountTobacooDto> bizCustomerAccountTobacooDtos = new ArrayList<>();
        List<BizCustomerAccountTobacoo> bizCustomerAccountTobacoos = bizCustomerAccountTobacooMapper.getList(bizCustomerAccountTobacoo);
        if (CollectionUtils.isNotEmpty(bizCustomerAccountTobacoos)) {
            bizCustomerAccountTobacooDtos = bizCustomerAccountTobacoos.stream().map(head -> {
                BizCustomerAccountTobacooDto dto = bizCustomerAccountTobacooDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizCustomerAccountTobacooDtos;
    }
    public ResultObject sendApproval(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("发送审批成功"));

        BizCustomerAccountTobacoo bizCustomerAccountTobacoo = bizCustomerAccountTobacooMapper.selectByPrimaryKey(sid);
        if (bizCustomerAccountTobacoo == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        if (!CommonEnum.OrderStatusEnum.CONFIRMED.getValue().equals(bizCustomerAccountTobacoo.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("请将进口计划操作确认再发送审批");
            return resultObject;
        }
        // 更新审批状态为2（审批中）
        bizCustomerAccountTobacoo.setApprStatus(CommonEnum.OrderApprStatusEnum.APPROVING.getValue());
        updateApprovalStatus(bizCustomerAccountTobacoo); // 调用更新审批状态的方法

        return resultObject;
    }
    @Transactional(rollbackFor = Exception.class)
    public void updateApprovalStatus(BizCustomerAccountTobacoo bizCustomerAccountTobacoo) {
        BizCustomerAccountTobacoo update = new BizCustomerAccountTobacoo();
        update.setSid(bizCustomerAccountTobacoo.getSid());
        update.setApprStatus(bizCustomerAccountTobacoo.getApprStatus());
        bizCustomerAccountTobacooMapper.updateByPrimaryKeySelective(update);
    }
    public ResultObject confirmStatus(BizCustomerAccountTobacooParam bizCustomerAccountTobacooParam, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("确认成功"));

        BizCustomerAccountTobacoo bizCustomerAccountTobacoo = bizCustomerAccountTobacooMapper.selectByPrimaryKey(bizCustomerAccountTobacooParam.getSid());
        if (bizCustomerAccountTobacoo == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        if (CommonEnum.OrderStatusEnum.CONFIRMED.getValue().equals(bizCustomerAccountTobacoo.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("该数据已经确认，无需重复操作");
            return resultObject;
        }
        BizCustomerAccountTobacoo update = bizCustomerAccountTobacooDtoMapper.toPo(bizCustomerAccountTobacooParam);
        update.setStatus(CommonEnum.OrderStatusEnum.CONFIRMED.getValue());
        update.setConfirmTime(new Date());
        update.setIsConfirm("1");
        bizCustomerAccountTobacooMapper.updateByPrimaryKeySelective(update);
        BizCustomerAccountTobacooDto dto = bizCustomerAccountTobacooDtoMapper.toDto(update);
        resultObject.setData(dto);
        return resultObject;
    }
    public ResultObject invalidate(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("作废成功"));

        BizCustomerAccountTobacoo bizCustomerAccountTobacoo = bizCustomerAccountTobacooMapper.selectByPrimaryKey(sid);
        if (bizCustomerAccountTobacoo == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        // 检查审批状态
        if (CommonEnum.OrderApprStatusEnum.APPROVING.getValue().equals(bizCustomerAccountTobacoo.getApprStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("审批中的数据不允许作废");
            return resultObject;
        }
        if (CommonEnum.OrderStatusEnum.CANCELLED.getValue().equals(bizCustomerAccountTobacoo.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("已作废的数据不允许作废");
            return resultObject;
        }
        BizCustomerAccountTobacoo update = new BizCustomerAccountTobacoo();
        update.setSid(sid);
        update.setStatus(CommonEnum.OrderStatusEnum.CANCELLED.getValue());
        bizCustomerAccountTobacooMapper.updateByPrimaryKeySelective(update);
        return resultObject;
    }

    public ResultObject getSupplierList(BizCustomerAccountTobacooParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功！");
        // 获取当前企业的供应商信息
        List<Map<String,String>>  list  = bizCustomerAccountTobacooMapper.getOrderSupplierList(userInfo.getCompany());
        if (CollectionUtils.isEmpty(list)){
            resultObject.setMessage("未查询到相关供应商信息");
            return resultObject;
        }
        resultObject.setData(list);
        return resultObject;
    }
    public ResultObject getCreateUserList(BizCustomerAccountTobacooParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功！");
        // 获取当前企业的供应商信息
        List<Map<String,String>>  list  = bizCustomerAccountTobacooMapper.getCreateUserList(userInfo.getCompany());
        if (CollectionUtils.isEmpty(list)){
            resultObject.setMessage("未查询到相关制单人信息");
            return resultObject;
        }
        resultObject.setData(list);
        return resultObject;
    }
    public String getSerialNo(UserInfoToken userInfo){
        String head = "YJSKHJS";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        String date = sdf.format(new Date());
        String serialNo = "";
        String serialNo1 = bizCustomerAccountTobacooMapper.getSerialNo(head + date);
        if(StringUtils.isNotBlank(serialNo1)){
            serialNo = head + date + String.format("%03d",Integer.parseInt(serialNo1.substring(serialNo1.length() - 3)) + 1);
        }else {
            serialNo = head + date + "001";
        }
        return serialNo;
    }
    public ResponseEntity print(BizCustomerAccountTobacooParam param, UserInfoToken userInfo) {
        return null;
    }
}
