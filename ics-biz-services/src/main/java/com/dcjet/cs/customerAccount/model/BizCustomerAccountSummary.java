package com.dcjet.cs.customerAccount.model;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2025-7-1
 */
@Setter
@Getter
@Table(name = "t_biz_customer_account_summary")
public class BizCustomerAccountSummary implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 主键
     */
	 @Id
	@Column(name = "sid")
	private  String sid;
	/**
     * 创建人
     */
	@Column(name = "create_by")
	private  String createBy;
	/**
     * 创建时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "create_time")
	private  Date createTime;
	/**
     * 创建人名称
     */
	@Column(name = "create_user_name")
	private  String createUserName;
	/**
     * 创建人
     */
	@Column(name = "update_by")
	private  String updateBy;
	/**
     * 最后修改时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "update_time")
	private  Date updateTime;
	/**
     * 最后修改人名称
     */
	@Column(name = "update_user_name")
	private  String updateUserName;
	/**
     * 企业编码
     */
	@Column(name = "trade_code")
	private  String tradeCode;
	/**
     * 创建人部门编码
     */
	@Column(name = "sys_org_code")
	private  String sysOrgCode;
	/**
     * 拓展字段1
     */
	@Column(name = "extend1")
	private  String extend1;
	/**
     * 拓展字段2
     */
	@Column(name = "extend2")
	private  String extend2;
	/**
     * 拓展字段3
     */
	@Column(name = "extend3")
	private  String extend3;
	/**
     * 拓展字段4
     */
	@Column(name = "extend4")
	private  String extend4;
	/**
     * 拓展字段5
     */
	@Column(name = "extend5")
	private  String extend5;
	/**
     * 拓展字段6
     */
	@Column(name = "extend6")
	private  String extend6;
	/**
     * 拓展字段7
     */
	@Column(name = "extend7")
	private  String extend7;
	/**
     * 拓展字段8
     */
	@Column(name = "extend8")
	private  String extend8;
	/**
     * 拓展字段9
     */
	@Column(name = "extend9")
	private  String extend9;
	/**
     * 拓展字段10
     */
	@Column(name = "extend10")
	private  String extend10;
	/**
     * 业务类型
     */
	@Column(name = "business_type")
	private  String businessType;
	/**
     * 结算单号
     */
	@Column(name = "account_no")
	private  String accountNo;
	/**
     * 合同号
     */
	@Column(name = "contract_no")
	private  String contractNo;
	/**
     * 币种
     */
	@Column(name = "curr")
	private  String curr;
	/**
     * 汇率
     */
	@Column(name = "exchange_rate")
	private  BigDecimal exchangeRate;
	/**
     * 货款
     */
	@Column(name = "goods_price")
	private  BigDecimal goodsPrice;
	/**
     * 代理费率%
     */
	@Column(name = "agent_fee_rate")
	private  BigDecimal agentFeeRate;
	/**
     * 代理费（不含税）
     */
	@Column(name = "agent_fee")
	private  BigDecimal agentFee;
	/**
     * 代理费税额
     */
	@Column(name = "agent_tax_fee")
	private  BigDecimal agentTaxFee;
	/**
     * 代理费（价税合计）
     */
	@Column(name = "agent_fee_total")
	private  BigDecimal agentFeeTotal;
	/**
     * 结算日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "business_date")
	private  Date businessDate;
	/**
     * 商品名称
     */
	@Column(name = "g_name")
	@JsonProperty("gName")
	private  String gName;
	/**
     * 发送财务系统
     */
	@Column(name = "send_finance")
	private  String sendFinance;
	/**
     * 商品与数量
     */
	@Column(name = "producr_some")
	private  String producrSome;
	/**
     * 备注
     */
	@Column(name = "note")
	private  String note;
	/**
     * 是否红冲
     */
	@Column(name = "red_flush")
	private  String redFlush;
	/**
     * 单据状态
     */
	@Column(name = "status")
	private  String status;
	/**
     * 审核状态
     */
	@Column(name = "appr_status")
	private  String apprStatus;
	/**
     * 确认时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "confirm_time")
	private  Date confirmTime;
	/**
     * 是否确认
     */
	@Column(name = "is_confirm")
	private  String isConfirm;
	/**
     * 外商合同、进货明细数据标记
     */
	@Column(name = "purchase_mark")
	private  String purchaseMark;
	/**
     * 外商合同、进货明细数据标记
     */
	@Column(name = "purchase_no_mark")
	private  String purchaseNoMark;
	/**
     * 人民币货款
     */
	@Column(name = "goods_price_rmb")
	private  BigDecimal goodsPriceRmb;
	/**
     * 增值税率%
     */
	@Column(name = "vat_rate")
	private  BigDecimal vatRate;
	/**
     * 货代费
     */
	@Column(name = "freight_forwarding_fee")
	private  BigDecimal freightForwardingFee;
	/**
     * 保险费
     */
	@Column(name = "insurance_fee")
	private  BigDecimal insuranceFee;
	/**
     * 已结算款项合计
     */
	@Column(name = "cost_fee")
	private  BigDecimal costFee;
	/**
     * 实际预收款
     */
	@Column(name = "deposit_received")
	private  BigDecimal depositReceived;
	/**
     * 应退款项
     */
	@Column(name = "refund_fee")
	private  BigDecimal refundFee;
	@Column(name = "customer")
	private  String customer;

	@Transient
	private String createTimeFrom;
	@Transient
	private String createTimeTo;

	@Transient
	private String createrBy;

	@Transient
	private String createrUserName;

	@Transient
	private Date createrTime;
}
