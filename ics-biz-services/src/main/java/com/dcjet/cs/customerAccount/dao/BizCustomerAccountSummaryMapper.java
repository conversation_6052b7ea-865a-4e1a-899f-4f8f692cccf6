package com.dcjet.cs.customerAccount.dao;
import com.dcjet.cs.customerAccount.model.BizCustomerAccountSummary;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
import java.util.Map;

/**
* generated by Generate 神码
* BizCustomerAccountSummary
* <AUTHOR>
* @date: 2025-7-1
*/
public interface BizCustomerAccountSummaryMapper extends Mapper<BizCustomerAccountSummary> {
    /**
     * 查询获取数据
     * @param bizCustomerAccountSummary
     * @return
     */
    List<BizCustomerAccountSummary> getList(BizCustomerAccountSummary bizCustomerAccountSummary);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    String getSerialNo(String prefix);

    List<Map<String, String>> getOrderSupplierList(String company);

    List<Map<String, String>> getCreateUserList(String company);
}
