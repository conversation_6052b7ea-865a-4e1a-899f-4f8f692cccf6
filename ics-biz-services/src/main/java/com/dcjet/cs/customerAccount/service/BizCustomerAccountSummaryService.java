package com.dcjet.cs.customerAccount.service;
import com.dcjet.cs.customerAccount.dao.BizCustomerAccountTobacooMapper;
import com.dcjet.cs.customerAccount.model.BizCustomerAccount;
import com.dcjet.cs.util.CommonEnum;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.customerAccount.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.customerAccount.dao.BizCustomerAccountSummaryMapper;
import com.dcjet.cs.customerAccount.mapper.BizCustomerAccountSummaryDtoMapper;
import com.dcjet.cs.customerAccount.model.BizCustomerAccountSummary;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import jdk.nashorn.internal.runtime.options.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-7-1
 */
@Service
public class BizCustomerAccountSummaryService extends BaseService<BizCustomerAccountSummary> {
    @Resource
    private BizCustomerAccountSummaryMapper bizCustomerAccountSummaryMapper;
    @Resource
    private BizCustomerAccountSummaryDtoMapper bizCustomerAccountSummaryDtoMapper;
    @Override
    public Mapper<BizCustomerAccountSummary> getMapper() {
        return bizCustomerAccountSummaryMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizCustomerAccountSummaryParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizCustomerAccountSummaryDto>> getListPaged(BizCustomerAccountSummaryParam bizCustomerAccountSummaryParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizCustomerAccountSummary bizCustomerAccountSummary = bizCustomerAccountSummaryDtoMapper.toPo(bizCustomerAccountSummaryParam);
        bizCustomerAccountSummary.setTradeCode(userInfo.getCompany());
        Page<BizCustomerAccountSummary> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizCustomerAccountSummaryMapper.getList(bizCustomerAccountSummary));
        List<BizCustomerAccountSummaryDto> bizCustomerAccountSummaryDtos = page.getResult().stream().map(head -> {
            BizCustomerAccountSummaryDto dto = bizCustomerAccountSummaryDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizCustomerAccountSummaryDto>> paged = ResultObject.createInstance(bizCustomerAccountSummaryDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizCustomerAccountSummaryParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizCustomerAccountSummaryDto insert(BizCustomerAccountSummaryParam bizCustomerAccountSummaryParam, UserInfoToken userInfo) {
        BizCustomerAccountSummary bizCustomerAccountSummary = bizCustomerAccountSummaryDtoMapper.toPo(bizCustomerAccountSummaryParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizCustomerAccountSummary.setSid(sid);
        bizCustomerAccountSummary.setCreateBy(userInfo.getUserNo());
        bizCustomerAccountSummary.setCreateTime(new Date());
        bizCustomerAccountSummary.setCreateUserName(userInfo.getUserName());
        bizCustomerAccountSummary.setBusinessType("3");
        bizCustomerAccountSummary.setApprStatus("0");
        bizCustomerAccountSummary.setStatus("0");
        bizCustomerAccountSummary.setSendFinance("0");
        bizCustomerAccountSummary.setBusinessDate(new Date());
        // 新增数据
        int insertStatus = bizCustomerAccountSummaryMapper.insert(bizCustomerAccountSummary);
        return  insertStatus > 0 ? bizCustomerAccountSummaryDtoMapper.toDto(bizCustomerAccountSummary) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizCustomerAccountSummaryParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizCustomerAccountSummaryDto update(BizCustomerAccountSummaryParam bizCustomerAccountSummaryParam, UserInfoToken userInfo) {
        BizCustomerAccountSummary bizCustomerAccountSummary = bizCustomerAccountSummaryMapper.selectByPrimaryKey(bizCustomerAccountSummaryParam.getSid());
        bizCustomerAccountSummaryDtoMapper.updatePo(bizCustomerAccountSummaryParam, bizCustomerAccountSummary);
        bizCustomerAccountSummary.setUpdateBy(userInfo.getUserNo());
        bizCustomerAccountSummary.setUpdateTime(new Date());
        bizCustomerAccountSummary.setUpdateUserName(userInfo.getUserName());
        // 更新数据
        int update = bizCustomerAccountSummaryMapper.updateByPrimaryKey(bizCustomerAccountSummary);
        return update > 0 ? bizCustomerAccountSummaryDtoMapper.toDto(bizCustomerAccountSummary) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		bizCustomerAccountSummaryMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizCustomerAccountSummaryDto> selectAll(BizCustomerAccountSummaryParam exportParam, UserInfoToken userInfo) {
        BizCustomerAccountSummary bizCustomerAccountSummary = bizCustomerAccountSummaryDtoMapper.toPo(exportParam);
         bizCustomerAccountSummary.setTradeCode(userInfo.getCompany());
        List<BizCustomerAccountSummaryDto> bizCustomerAccountSummaryDtos = new ArrayList<>();
        List<BizCustomerAccountSummary> bizCustomerAccountSummarys = bizCustomerAccountSummaryMapper.getList(bizCustomerAccountSummary);
        if (CollectionUtils.isNotEmpty(bizCustomerAccountSummarys)) {
            bizCustomerAccountSummaryDtos = bizCustomerAccountSummarys.stream().map(head -> {
                BizCustomerAccountSummaryDto dto = bizCustomerAccountSummaryDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizCustomerAccountSummaryDtos;
    }
    public ResultObject sendApproval(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("发送审批成功"));

        BizCustomerAccountSummary bizCustomerAccountSummary = bizCustomerAccountSummaryMapper.selectByPrimaryKey(sid);
        if (bizCustomerAccountSummary == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        if (!CommonEnum.OrderStatusEnum.CONFIRMED.getValue().equals(bizCustomerAccountSummary.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("请将进口计划操作确认再发送审批");
            return resultObject;
        }
        // 更新审批状态为2（审批中）
        bizCustomerAccountSummary.setApprStatus(CommonEnum.OrderApprStatusEnum.APPROVING.getValue());
        updateApprovalStatus(bizCustomerAccountSummary); // 调用更新审批状态的方法

        return resultObject;
    }
    @Transactional(rollbackFor = Exception.class)
    public void updateApprovalStatus(BizCustomerAccountSummary bizCustomerAccountSummary) {
        BizCustomerAccountSummary update = new BizCustomerAccountSummary();
        update.setSid(bizCustomerAccountSummary.getSid());
        update.setApprStatus(bizCustomerAccountSummary.getApprStatus());
        bizCustomerAccountSummaryMapper.updateByPrimaryKeySelective(update);
    }
    public ResultObject confirmStatus(BizCustomerAccountSummaryParam bizCustomerAccountSummaryParam, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("确认成功"));

        BizCustomerAccountSummary bizCustomerAccountSummary = bizCustomerAccountSummaryMapper.selectByPrimaryKey(bizCustomerAccountSummaryParam.getSid());
        if (bizCustomerAccountSummary == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        if (CommonEnum.OrderStatusEnum.CONFIRMED.getValue().equals(bizCustomerAccountSummary.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("该数据已经确认，无需重复操作");
            return resultObject;
        }
        BizCustomerAccountSummary update = bizCustomerAccountSummaryDtoMapper.toPo(bizCustomerAccountSummaryParam);
        update.setStatus(CommonEnum.OrderStatusEnum.CONFIRMED.getValue());
        update.setConfirmTime(new Date());
        update.setIsConfirm("1");
        bizCustomerAccountSummaryMapper.updateByPrimaryKeySelective(update);
        BizCustomerAccountSummaryDto dto = bizCustomerAccountSummaryDtoMapper.toDto(update);
        resultObject.setData(dto);
        return resultObject;
    }
    public ResultObject invalidate(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("作废成功"));

        BizCustomerAccountSummary bizCustomerAccountSummary = bizCustomerAccountSummaryMapper.selectByPrimaryKey(sid);
        if (bizCustomerAccountSummary == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        // 检查审批状态
        if (CommonEnum.OrderApprStatusEnum.APPROVING.getValue().equals(bizCustomerAccountSummary.getApprStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("审批中的数据不允许作废");
            return resultObject;
        }
        if (CommonEnum.OrderStatusEnum.CANCELLED.getValue().equals(bizCustomerAccountSummary.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("已作废的数据不允许作废");
            return resultObject;
        }
        BizCustomerAccountSummary update = new BizCustomerAccountSummary();
        update.setSid(sid);
        update.setStatus(CommonEnum.OrderStatusEnum.CANCELLED.getValue());
        bizCustomerAccountSummaryMapper.updateByPrimaryKeySelective(update);
        return resultObject;
    }

    public String getSerialNo(UserInfoToken userInfo){
        String head = "YJSKHJS";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        String date = sdf.format(new Date());
        String serialNo = "";
        String serialNo1 = bizCustomerAccountSummaryMapper.getSerialNo(head + date);
        if(StringUtils.isNotBlank(serialNo1)){
            serialNo = head + date + String.format("%03d",Integer.parseInt(serialNo1.substring(serialNo1.length() - 3)) + 1);
        }else {
            serialNo = head + date + "001";
        }
        return serialNo;
    }
    public ResponseEntity printContact(BizCustomerAccountSummaryParam param, UserInfoToken userInfo) {
        return null;
    }
    public ResponseEntity printSummary(BizCustomerAccountSummaryParam param, UserInfoToken userInfo) {
        return null;
    }

    public ResultObject getSupplierList(BizCustomerAccountSummaryParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功！");
        // 获取当前企业的供应商信息
        List<Map<String,String>>  list  = bizCustomerAccountSummaryMapper.getOrderSupplierList(userInfo.getCompany());
        if (CollectionUtils.isEmpty(list)){
            resultObject.setMessage("未查询到相关供应商信息");
            return resultObject;
        }
        resultObject.setData(list);
        return resultObject;
    }
    public ResultObject getCreateUserList(BizCustomerAccountSummaryParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功！");
        // 获取当前企业的供应商信息
        List<Map<String,String>>  list  = bizCustomerAccountSummaryMapper.getCreateUserList(userInfo.getCompany());
        if (CollectionUtils.isEmpty(list)){
            resultObject.setMessage("未查询到相关制单人信息");
            return resultObject;
        }
        resultObject.setData(list);
        return resultObject;
    }
}
