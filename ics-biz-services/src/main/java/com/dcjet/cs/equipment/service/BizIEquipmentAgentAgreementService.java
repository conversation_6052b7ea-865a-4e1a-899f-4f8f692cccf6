package com.dcjet.cs.equipment.service;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.equipment.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.equipment.dao.BizIEquipmentAgentAgreementMapper;
import com.dcjet.cs.equipment.mapper.BizIEquipmentAgentAgreementDtoMapper;
import com.dcjet.cs.equipment.model.BizIEquipmentAgentAgreement;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import jdk.nashorn.internal.runtime.options.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.util.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-7-2
 */
@Service
public class BizIEquipmentAgentAgreementService extends BaseService<BizIEquipmentAgentAgreement> {
    @Resource
    private BizIEquipmentAgentAgreementMapper bizIEquipmentAgentAgreementMapper;
    @Resource
    private BizIEquipmentAgentAgreementDtoMapper bizIEquipmentAgentAgreementDtoMapper;
    @Override
    public Mapper<BizIEquipmentAgentAgreement> getMapper() {
        return bizIEquipmentAgentAgreementMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizIEquipmentAgentAgreementParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizIEquipmentAgentAgreementDto>> getListPaged(BizIEquipmentAgentAgreementParam bizIEquipmentAgentAgreementParam, PageParam pageParam) {
        // 启用分页查询
        BizIEquipmentAgentAgreement bizIEquipmentAgentAgreement = bizIEquipmentAgentAgreementDtoMapper.toPo(bizIEquipmentAgentAgreementParam);
        Page<BizIEquipmentAgentAgreement> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizIEquipmentAgentAgreementMapper.getList(bizIEquipmentAgentAgreement));
        List<BizIEquipmentAgentAgreementDto> bizIEquipmentAgentAgreementDtos = page.getResult().stream().map(head -> {
            BizIEquipmentAgentAgreementDto dto = bizIEquipmentAgentAgreementDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizIEquipmentAgentAgreementDto>> paged = ResultObject.createInstance(bizIEquipmentAgentAgreementDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizIEquipmentAgentAgreementParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIEquipmentAgentAgreementDto insert(BizIEquipmentAgentAgreementParam bizIEquipmentAgentAgreementParam, UserInfoToken userInfo) {
        BizIEquipmentAgentAgreement bizIEquipmentAgentAgreement = bizIEquipmentAgentAgreementDtoMapper.toPo(bizIEquipmentAgentAgreementParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizIEquipmentAgentAgreement.setSid(sid);
        bizIEquipmentAgentAgreement.setInsertUser(userInfo.getUserNo());
        bizIEquipmentAgentAgreement.setInsertTime(new Date());
        // 新增数据
        int insertStatus = bizIEquipmentAgentAgreementMapper.insert(bizIEquipmentAgentAgreement);
        return  insertStatus > 0 ? bizIEquipmentAgentAgreementDtoMapper.toDto(bizIEquipmentAgentAgreement) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizIEquipmentAgentAgreementParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIEquipmentAgentAgreementDto update(BizIEquipmentAgentAgreementParam bizIEquipmentAgentAgreementParam, UserInfoToken userInfo) {
        BizIEquipmentAgentAgreement bizIEquipmentAgentAgreement = bizIEquipmentAgentAgreementMapper.selectByPrimaryKey(bizIEquipmentAgentAgreementParam.getSid());
        bizIEquipmentAgentAgreementDtoMapper.updatePo(bizIEquipmentAgentAgreementParam, bizIEquipmentAgentAgreement);
        bizIEquipmentAgentAgreement.setUpdateUser(userInfo.getUserNo());
        bizIEquipmentAgentAgreement.setUpdateTime(new Date());
        // 更新数据
        int update = bizIEquipmentAgentAgreementMapper.updateByPrimaryKey(bizIEquipmentAgentAgreement);
        return update > 0 ? bizIEquipmentAgentAgreementDtoMapper.toDto(bizIEquipmentAgentAgreement) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		bizIEquipmentAgentAgreementMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizIEquipmentAgentAgreementDto> selectAll(BizIEquipmentAgentAgreementParam exportParam, UserInfoToken userInfo) {
        BizIEquipmentAgentAgreement bizIEquipmentAgentAgreement = bizIEquipmentAgentAgreementDtoMapper.toPo(exportParam);
        // bizIEquipmentAgentAgreement.setTradeCode(userInfo.getCompany());
        List<BizIEquipmentAgentAgreementDto> bizIEquipmentAgentAgreementDtos = new ArrayList<>();
        List<BizIEquipmentAgentAgreement> bizIEquipmentAgentAgreements = bizIEquipmentAgentAgreementMapper.getList(bizIEquipmentAgentAgreement);
        if (CollectionUtils.isNotEmpty(bizIEquipmentAgentAgreements)) {
            bizIEquipmentAgentAgreementDtos = bizIEquipmentAgentAgreements.stream().map(head -> {
                BizIEquipmentAgentAgreementDto dto = bizIEquipmentAgentAgreementDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizIEquipmentAgentAgreementDtos;
    }
}
