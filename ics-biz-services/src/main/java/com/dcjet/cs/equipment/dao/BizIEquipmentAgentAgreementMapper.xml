<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.equipment.dao.BizIEquipmentAgentAgreementMapper">
    <resultMap id="bizIEquipmentAgentAgreementResultMap" type="com.dcjet.cs.equipment.model.BizIEquipmentAgentAgreement">
		<id column="id" property="id" jdbcType="VARCHAR" />
		<result column="business_type" property="businessType" jdbcType="VARCHAR" />
		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
		<result column="sys_org_code" property="sysOrgCode" jdbcType="VARCHAR" />
		<result column="data_state" property="dataState" jdbcType="VARCHAR" />
		<result column="version_no" property="versionNo" jdbcType="VARCHAR" />
		<result column="parent_id" property="parentId" jdbcType="VARCHAR" />
		<result column="create_by" property="createBy" jdbcType="VARCHAR" />
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
		<result column="update_by" property="updateBy" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR" />
		<result column="update_user_name" property="updateUserName" jdbcType="VARCHAR" />
		<result column="extend1" property="extend1" jdbcType="VARCHAR" />
		<result column="extend2" property="extend2" jdbcType="VARCHAR" />
		<result column="extend3" property="extend3" jdbcType="VARCHAR" />
		<result column="extend4" property="extend4" jdbcType="VARCHAR" />
		<result column="extend5" property="extend5" jdbcType="VARCHAR" />
		<result column="extend6" property="extend6" jdbcType="VARCHAR" />
		<result column="extend7" property="extend7" jdbcType="VARCHAR" />
		<result column="extend8" property="extend8" jdbcType="VARCHAR" />
		<result column="extend9" property="extend9" jdbcType="VARCHAR" />
		<result column="extend10" property="extend10" jdbcType="VARCHAR" />
		<result column="contract_no" property="contractNo" jdbcType="VARCHAR" />
		<result column="business_place" property="businessPlace" jdbcType="VARCHAR" />
		<result column="agreement_type" property="agreementType" jdbcType="VARCHAR" />
		<result column="agreement_no" property="agreementNo" jdbcType="VARCHAR" />
		<result column="customer" property="customer" jdbcType="VARCHAR" />
		<result column="supplier" property="supplier" jdbcType="VARCHAR" />
		<result column="sign_date" property="signDate" jdbcType="TIMESTAMP" />
		<result column="sign_place" property="signPlace" jdbcType="VARCHAR" />
		<result column="currency" property="currency" jdbcType="VARCHAR" />
		<result column="contract_amount" property="contractAmount" jdbcType="NUMERIC" />
		<result column="agency_rate" property="agencyRate" jdbcType="NUMERIC" />
		<result column="agency_fee" property="agencyFee" jdbcType="NUMERIC" />
		<result column="agreement_terms" property="agreementTerms" jdbcType="VARCHAR" />
		<result column="make_by" property="makeBy" jdbcType="VARCHAR" />
		<result column="make_date" property="makeDate" jdbcType="TIMESTAMP" />
		<result column="bill_status" property="billStatus" jdbcType="VARCHAR" />
		<result column="confirm_time" property="confirmTime" jdbcType="TIMESTAMP" />
		<result column="approval_status" property="approvalStatus" jdbcType="VARCHAR" />
	</resultMap>
	<sql id="Base_Column_List" >
     id
     ,business_type
     ,trade_code
     ,sys_org_code
     ,data_state
     ,version_no
     ,parent_id
     ,create_by
     ,create_time
     ,update_by
     ,update_time
     ,insert_user_name
     ,update_user_name
     ,extend1
     ,extend2
     ,extend3
     ,extend4
     ,extend5
     ,extend6
     ,extend7
     ,extend8
     ,extend9
     ,extend10
     ,contract_no
     ,business_place
     ,agreement_type
     ,agreement_no
     ,customer
     ,supplier
     ,sign_date
     ,sign_place
     ,currency
     ,contract_amount
     ,agency_rate
     ,agency_fee
     ,agreement_terms
     ,make_by
     ,make_date
     ,bill_status
     ,confirm_time
     ,approval_status
    </sql>
    <sql id="condition">
    <if test="businessType != null and businessType != ''">
		and business_type = #{businessType}
	</if>
    <if test="tradeCode != null and tradeCode != ''">
		and trade_code = #{tradeCode}
	</if>
    <if test="contractNo != null and contractNo != ''">
	  and contract_no like '%'|| #{contractNo} || '%'
	</if>
    <if test="agreementNo != null and agreementNo != ''">
	  and agreement_no like '%'|| #{agreementNo} || '%'
	</if>
    <if test="customer != null and customer != ''">
		and customer = #{customer}
	</if>
    <if test="supplier != null and supplier != ''">
		and supplier = #{supplier}
	</if>
        <if test="_databaseId == 'oracle' and signDateFrom != null and signDateFrom != ''">
            <![CDATA[ and sign_date >= to_date(#{signDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="_databaseId == 'oracle' and signDateTo != null and signDateTo != ''">
            <![CDATA[ and sign_date <= to_date(#{signDateTo}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="_databaseId == 'postgresql' and signDateFrom != null and signDateFrom != ''">
            <![CDATA[ and sign_date >= to_date(#{signDateFrom}, 'yyyy-MM-dd hh24:mi:ss')::date]]>
        </if>
        <if test="_databaseId == 'postgresql' and signDateTo != null and signDateTo != ''">
            <![CDATA[ and sign_date <= to_date(#{signDateTo}, 'yyyy-MM-dd hh24:mi:ss')::date ]]>
        </if>
    <if test="makeBy != null and makeBy != ''">
	  and make_by like '%'|| #{makeBy} || '%'
	</if>
        <if test="_databaseId == 'oracle' and makeDateFrom != null and makeDateFrom != ''">
            <![CDATA[ and make_date >= to_date(#{makeDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="_databaseId == 'oracle' and makeDateTo != null and makeDateTo != ''">
            <![CDATA[ and make_date <= to_date(#{makeDateTo}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="_databaseId == 'postgresql' and makeDateFrom != null and makeDateFrom != ''">
            <![CDATA[ and make_date >= to_timestamp(#{makeDateFrom}, 'yyyy-MM-dd hh24:mi:ss')::timestamp]]>
        </if>
        <if test="_databaseId == 'postgresql' and makeDateTo != null and makeDateTo != ''">
            <![CDATA[ and make_date <= to_timestamp(#{makeDateTo}, 'yyyy-MM-dd hh24:mi:ss')::timestamp ]]>
        </if>
    <if test="billStatus != null and billStatus != ''">
		and bill_status = #{billStatus}
	</if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="bizIEquipmentAgentAgreementResultMap" parameterType="com.dcjet.cs.equipment.model.BizIEquipmentAgentAgreement">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        t_biz_i_equipment_agent_agreement t
        <where>
            <include refid="condition"></include>
        </where>
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from t_biz_i_equipment_agent_agreement t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
</mapper>
