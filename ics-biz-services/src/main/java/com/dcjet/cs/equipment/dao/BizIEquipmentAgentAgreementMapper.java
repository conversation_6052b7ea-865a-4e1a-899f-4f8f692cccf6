package com.dcjet.cs.equipment.dao;
import com.dcjet.cs.equipment.model.BizIEquipmentAgentAgreement;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
/**
* generated by Generate 神码
* BizIEquipmentAgentAgreement
* <AUTHOR>
* @date: 2025-7-2
*/
public interface BizIEquipmentAgentAgreementMapper extends Mapper<BizIEquipmentAgentAgreement> {
    /**
     * 查询获取数据
     * @param bizIEquipmentAgentAgreement
     * @return
     */
    List<BizIEquipmentAgentAgreement> getList(BizIEquipmentAgentAgreement bizIEquipmentAgentAgreement);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
}
