package com.dcjet.cs.equipment.dao;
import com.dcjet.cs.equipment.model.BizIEquipmentAgentAgreementList;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
import java.util.Map;
/**
* generated by Generate dcits
* BizIEquipmentAgentAgreementList
* <AUTHOR>
* @date: 2025-7-2
*/
public interface BizIEquipmentAgentAgreementListMapper extends Mapper<BizIEquipmentAgentAgreementList> {
    /**
     * 根据参数查询
     *
     * @param bizIEquipmentAgentAgreementList
     * @return
     */
    List<BizIEquipmentAgentAgreementList> getList(BizIEquipmentAgentAgreementList bizIEquipmentAgentAgreementList);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
    /**
     * 根据表头headId批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    void deleteByHeadIds(List<String> sids);
    /**
     * 根据表头headId查询是否存在表体数据
     * @param sids
     * @return
     */
    int getListNumByHeadIds(List<String> sids);
}
